<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Design Template Gallery - Sticker Generator</title> <!-- Updated Title -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="/styles.css">
    <style>
        body {
            background: #111;
            color: #fff;
            margin: 0;
            font-family: 'Inter', sans-serif;
            padding-top: 2rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        h1 {
            font-size: 2rem;
            font-weight: 600;
            margin: 0 0 1rem;
            color: #fff;
        }

        .lead {
            color: #aaa;
            margin-bottom: 2rem;
        }

        .inspiration-gallery { /* Keep class name for now, but it shows templates */
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }
        
        .inspiration-card { /* Keep class name for now */
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            transition: transform 0.3s, box-shadow 0.3s;
            background-color: #1a1a1a;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        
        .inspiration-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);
        }
        
        .inspiration-card img {
            width: 100%;
            aspect-ratio: 1/1;
            object-fit: cover;
            border-bottom: 1px solid #333;
        }
        
        .card-body {
            padding: 1.5rem;
            display: flex;
            flex-direction: column;
            flex-grow: 1;
        }
        
        .card-title {
            font-size: 1.25rem;
            margin-bottom: 0.5rem;
            color: #fff;
        }
        
        .card-text {
            color: #aaa;
            margin-bottom: 1rem;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2; /* Limit description lines */
            -webkit-box-orient: vertical;
        }
        
        .model-text {
            font-size: 0.85rem;
            color: #777;
            margin-bottom: 1rem;
        }
        
        .action-btn {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }
        
        .action-btn:hover {
            background-color: #45a049;
        }
        
        .inspiration-card-footer {
            padding: 1rem 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between; /* Adjust if only one button group */
            border-top: 1px solid #333;
            background-color: #222;
            margin-top: auto; /* Push footer to bottom */
        }
        
        .empty-state {
            text-align: center;
            padding: 3rem;
            background-color: #1a1a1a;
            border-radius: 8px;
            margin-top: 2rem;
        }
        
        .empty-state i {
            font-size: 3rem;
            color: #555;
            margin-bottom: 1rem;
        }
        
        .empty-state h3 {
            color: #ddd;
            margin-bottom: 1rem;
        }
        
        .empty-state p {
            color: #aaa;
            max-width: 500px;
            margin: 0 auto 1.5rem;
        }
        
        .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 3rem;
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #333;
            border-top: 5px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 1rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border-radius: 4px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary {
            background-color: #4CAF50;
            color: white;
            border: none;
        }

        .btn-primary:hover {
            background-color: #45a049;
        }

        .btn-secondary { /* Style for Load in Editor button */
             background-color: #555;
             color: white;
             border: none;
        }
        .btn-secondary:hover {
             background-color: #666;
        }

        .btn-outline-primary {
            background-color: transparent;
            color: #4CAF50;
            border: 1px solid #4CAF50;
        }

        .btn-outline-primary:hover {
            background-color: #4CAF50;
            color: white;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .header-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .d-none {
            display: none !important;
        }
    </style>
</head>
<body>
    <div id="app">
        <div id="topbar-container"></div>
        
        <div class="container">
            <div class="header-section">
                <h1>Design Template Gallery</h1> <!-- Updated Title -->
                <a href="/" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left"></i> Back to Generator
                </a>
            </div>
            
            <p class="lead">Browse through our collection of design templates and create your own variations.</p> <!-- Updated Text -->
            
            <div id="loading-container" class="loading-container"> <!-- Show initially -->
                <div class="loading-spinner"></div>
                <p>Loading templates...</p> <!-- Updated Text -->
            </div>
            
            <div id="inspiration-gallery" class="inspiration-gallery d-none"></div> <!-- Keep class name, hide initially -->
            
            <div id="empty-state" class="empty-state d-none">
                <i class="fas fa-palette"></i> <!-- Changed Icon -->
                <h3>No Design Templates Yet</h3> <!-- Updated Text -->
                <p>Create some designs in the editor and save them as templates to see them here!</p> <!-- Updated Text -->
                <a href="/" class="btn btn-primary">Go to Generator</a>
            </div>
        </div>
    </div>
    
    <script type="module">
        import { createTopbar } from './js/components/Topbar.js';
        import { showToast } from './js/components/Toast.js'; // Assuming Toast component exists
        
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                await createTopbar();
                const templates = await fetchTemplates(); // Use new function name
                
                document.getElementById('loading-container').classList.add('d-none');
                
                if (!templates || templates.length === 0) {
                    document.getElementById('empty-state').classList.remove('d-none');
                    return;
                }
                
                displayTemplates(templates); // Use new function name
                document.getElementById('inspiration-gallery').classList.remove('d-none');
                
                // Admin check is likely not needed for public gallery view
                // const isAdmin = await checkIfAdmin();
                // if (isAdmin) {
                //     document.body.classList.add('admin-user');
                // }
            } catch (error) {
                console.error('Error initializing page:', error);
                showToast('Failed to load templates', 'error'); // Updated message
                document.getElementById('loading-container').classList.add('d-none'); // Hide loading on error too
                document.getElementById('empty-state').classList.remove('d-none'); // Show empty state on error
            }
        });
        
        async function fetchTemplates() { // Renamed function
            try {
                // Fetch from the route that now returns Design Templates
                const response = await fetch('/api/inspirations'); 
                
                if (!response.ok) {
                    throw new Error('Failed to fetch templates'); // Updated message
                }
                
                return await response.json();
            } catch (error) {
                console.error('Error fetching templates:', error); // Updated message
                return [];
            }
        }
        
        // Admin check function might not be needed here anymore
        // async function checkIfAdmin() { ... } 
        
        // Function now displays Design Templates
        function displayTemplates(templates) { // Renamed function and parameter
            const gallery = document.getElementById('inspiration-gallery');
            gallery.innerHTML = '';
            
            templates.forEach((template, index) => {
                const imageNumber = String(index + 1).padStart(3, '0');
                const card = createTemplateCard(template, imageNumber); // Use new card function name
                gallery.appendChild(card);
            });
        }
        
        // Function now creates card based on Design Template data
        function createTemplateCard(template, imageNumber) { // Renamed function and parameter
            const card = document.createElement('div');
            card.className = 'inspiration-card'; // Keep class name for styling consistency
            
            // Image Container
            const imgContainer = document.createElement('div');
            imgContainer.style.position = 'relative';
            
            const img = document.createElement('img');
            // Use previewImageUrl, provide a fallback if necessary
            img.src = template.previewImageUrl || '/images/placeholder.png'; // Use template preview URL
            img.alt = template.name || 'Design Template Preview';
            img.loading = 'lazy';
            img.onerror = () => { img.src = '/images/placeholder.png'; }; // Fallback image on error
            imgContainer.appendChild(img);
            
            const numberOverlay = document.createElement('div');
            // ... (styling for numberOverlay remains the same) ...
             numberOverlay.style.position = 'absolute';
             numberOverlay.style.top = '10px';
             numberOverlay.style.right = '10px';
             numberOverlay.style.background = 'rgba(0, 0, 0, 0.7)';
             numberOverlay.style.color = 'red'; // Changed color for visibility
             numberOverlay.style.padding = '5px 10px';
             numberOverlay.style.borderRadius = '4px';
             numberOverlay.style.fontWeight = 'bold';
             numberOverlay.textContent = imageNumber;
            imgContainer.appendChild(numberOverlay);
            
            card.appendChild(imgContainer);
            
            // Card Body
            const body = document.createElement('div');
            body.className = 'card-body d-flex flex-column';
            
            const title = document.createElement('h5');
            title.className = 'card-title';
            title.textContent = template.name || 'Untitled Template'; // Use template name
            body.appendChild(title);
            
            // Use adminData.prompt for description
            const promptText = document.createElement('p');
            promptText.className = 'card-text small text-muted';
            promptText.textContent = template.adminData?.prompt || 'No description available.'; 
            body.appendChild(promptText);
            
            // Use adminData.model for model info
            const modelText = document.createElement('p');
            modelText.className = 'model-text small text-muted';
            if (template.adminData?.model) {
                modelText.textContent = `Model: ${template.adminData.model}`;
            } else {
                 modelText.textContent = 'Model: Not specified'; // Indicate if model is missing
            }
            body.appendChild(modelText);
            
            card.appendChild(body); // Append body before footer

            // Card Footer
            const footer = document.createElement('div');
            footer.className = 'inspiration-card-footer mt-auto'; // Ensure footer is at the bottom
            
            const buttonContainer = document.createElement('div');
            buttonContainer.style.display = 'flex';
            buttonContainer.style.gap = '10px';
            
            // "Use This Design" button -> links to generate page with TEMPLATE ID
            const useBtn = document.createElement('button');
            useBtn.className = 'btn btn-primary btn-sm';
            useBtn.innerHTML = '<i class="fas fa-magic"></i> Use This Design';
            useBtn.onclick = () => {
                window.location.href = `/generate-from-inspiration.html?id=${template._id}`; // Use template ID
            };
            buttonContainer.appendChild(useBtn);
            
            // "Load in Editor" button -> links directly to editor with TEMPLATE ID
            const loadBtn = document.createElement('button');
            loadBtn.className = 'btn btn-secondary btn-sm';
            loadBtn.innerHTML = '<i class="fas fa-edit"></i> Load in Editor';
            loadBtn.onclick = () => {
                const editorUrl = new URL('/design-editor.html', window.location.origin);
                editorUrl.searchParams.set('templateId', template._id); // Use template ID
                window.location.href = editorUrl.toString();
            };
            buttonContainer.appendChild(loadBtn);
            
            footer.appendChild(buttonContainer);
            card.appendChild(footer); // Append footer last
            
            return card;
        }
        
        // Admin delete function is removed as it's not needed for public view
        
    </script>
</body>
</html>
