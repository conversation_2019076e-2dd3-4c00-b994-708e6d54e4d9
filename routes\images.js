import express from 'express';
import { auth } from '../middleware/auth.js';
import { storage } from '../utils/storage.js';
import multer from 'multer';
import Replicate from 'replicate';
import fetch from 'node-fetch';
import Generation from '../models/Generation.js'; // Import the Generation model

const router = express.Router();
const upload = multer();

// Initialize Replicate client
const replicate = new Replicate({
    auth: process.env.REPLICATE_API_TOKEN,
});

// Upload an image
router.post('/upload', auth, upload.single('image'), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ error: 'No image file provided' });
        }

        const { url, publicUrl, fileName } = await storage.uploadBuffer(req.file.buffer);

        const generatedUrl = publicUrl || url || `https://f005.backblazeb2.com/file/stickers-replicate-app/${fileName}`;

        res.json({
            success: true,
            imageUrl: generatedUrl,
            fileName: fileName
        });
    } catch (error) {
        console.error('Error uploading image:', error);
        res.status(500).json({ error: 'Failed to upload image' });
    }
});

// Upscale an image
router.post('/upscale', auth, async (req, res) => {
    try {
        const { imageUrl } = req.body;
        console.log('Upscaling image request body:', req.body);
        console.log('Image URL to upscale:', imageUrl);

        if (!imageUrl) {
            return res.status(400).json({ error: 'Image URL is required' });
        }

        // Validate and encode URL
        let validatedUrl;
        try {
            // First decode the URL in case it's already encoded
            const decodedUrl = decodeURIComponent(imageUrl);
            validatedUrl = new URL(decodedUrl);
            if (!validatedUrl.protocol.startsWith('http')) {
                throw new Error('Invalid protocol');
            }
            // Re-encode the URL properly
            validatedUrl = new URL(validatedUrl.href);
        } catch (e) {
            console.error('Invalid URL:', imageUrl, e);
            return res.status(400).json({ error: 'Invalid image URL provided' });
        }

        // Download the original image first
        console.log('Attempting to fetch original image from:', validatedUrl.href);
        const originalResponse = await fetch(validatedUrl.href);
        if (!originalResponse.ok) {
            console.error('Failed to fetch original image:', originalResponse.status, originalResponse.statusText);
            throw new Error('Failed to fetch original image');
        }
        const originalBuffer = await originalResponse.buffer();
        console.log('Successfully downloaded original image');

        // Upload original to get a clean URL
        const originalUpload = await storage.uploadBuffer(originalBuffer);
        console.log('Original image uploaded:', originalUpload);

        // Run upscale model
        const modelUrl = originalUpload.publicUrl || originalUpload.url;
        console.log('Running upscale model with URL:', modelUrl);

        const output = await replicate.run(
            "nightmareai/real-esrgan:f121d640bd286e1fdc67f9799164c1d5be36ff74576ee11c803ae5b665dd46aa",
            {
                input: {
                    image: modelUrl,
                    scale: 2
                }
            }
        );

        console.log('Raw model output:', output);

        if (!output || typeof output !== 'string' || !output.startsWith('http')) {
            console.error('Invalid output from upscale model:', output);
            throw new Error('Invalid output from upscale model');
        }

        // Download the upscaled image
        console.log('Downloading upscaled image from:', output);
        const upscaledResponse = await fetch(output);
        if (!upscaledResponse.ok) {
            console.error('Failed to fetch upscaled image:', upscaledResponse.status, upscaledResponse.statusText);
            throw new Error('Failed to fetch upscaled image');
        }
        const upscaledBuffer = await upscaledResponse.buffer();
        console.log('Successfully downloaded upscaled image');

        // Upload to storage
        const uploadResult = await storage.uploadBuffer(upscaledBuffer);
        console.log('Uploaded upscaled image:', uploadResult);

        res.json({
            success: true,
            imageUrl: uploadResult.publicUrl || uploadResult.url
        });

    } catch (error) {
        console.error('Error upscaling image:', error);
        res.status(500).json({ error: error.message || 'Failed to upscale image' });
    }
});

// --- Reusable Background Removal Function ---
export async function performBackgroundRemoval(imageUrl, generationId, userId) {
    console.log('[BG Remove - Function] Starting background removal process.');
    console.log('[BG Remove - Function] generationId:', generationId, 'imageUrl:', imageUrl, 'userId:', userId);

    if (!imageUrl || !generationId || !userId) {
        console.error('[BG Remove - Function] Error: Missing imageUrl, generationId, or userId.');
        throw new Error('Image URL, Generation ID, and User ID are required for background removal.');
    }

    // Find the original generation document to update
    console.log('[BG Remove - Function] Verifying ownership for Generation ID:', generationId, 'and userId:', userId);
    const originalGeneration = await Generation.findOne({
        _id: generationId,
        userId: userId // Ensure user owns the generation
    });

    if (!originalGeneration) {
         console.error('[BG Remove - Function] Original generation not found for ID:', generationId, 'and userId:', userId);
         throw new Error('Original image generation record not found or access denied');
    }
    console.log('[BG Remove - Function] Found original Generation document:', originalGeneration._id);
    console.log(`[BG Remove - Function] Current imageUrl in DB: ${originalGeneration.imageUrl}`);

    // Use the imageUrl passed to the function (should be the B2 URL)
    const imageUrlForModel = imageUrl;

    // Run background removal model
    console.log('[BG Remove - Function] Calling Replicate API with image:', imageUrlForModel);
    const output = await replicate.run(
        "codeplugtech/background_remover:37ff2aa89897c0de4a140a3d50969dc62b663ea467e1e2bde18008e3d3731b2b",
        {
            input: {
                image: imageUrlForModel
            }
        }
    );
    console.log('[BG Remove - Function] Replicate API response:', output);

    if (!output || typeof output !== 'string' || !output.startsWith('http')) {
         console.error('[BG Remove - Function] Invalid output from Replicate:', output);
        throw new Error('Invalid output from background removal model');
    }

    // Get the processed image URL from Replicate
    const processedImageUrl = output;
    console.log('[BG Remove - Function] Processed image URL from Replicate:', processedImageUrl);

    // Save the processed image (from Replicate URL) to B2 storage
    console.log('[BG Remove - Function] Saving processed image to B2...');
    // Use a distinct prefix like 'nobg' or similar if desired, or keep 'generations'
    const savedImage = await storage.saveImageFromUrl(processedImageUrl, 'generations', userId);
    console.log('[BG Remove - Function] Saved image details:', savedImage);

    if (!savedImage || !savedImage.publicUrl) {
        console.error('[BG Remove - Function] Failed to save image or get public URL.');
        throw new Error('Failed to save processed image');
    }
    const newImageUrl = savedImage.publicUrl; // This is the final B2 URL
    console.log(`[BG Remove - Function] New image URL saved to B2: ${newImageUrl}`);

    // --- Update Original Generation Record in DB ---
    console.log(`[BG Remove - Function] Updating original Generation record ${originalGeneration._id}.`);
    console.log(`[BG Remove - Function] New imageUrl to save in DB: ${newImageUrl}`);
    try {
        originalGeneration.imageUrl = newImageUrl; // Update the image URL
        originalGeneration.prompt = `${originalGeneration.prompt || 'Image'} [BG Removed]`; // Optionally update prompt
        originalGeneration.updatedAt = new Date(); // Update timestamp

        console.log(`[BG Remove - Function] Attempting to save Generation ${originalGeneration._id}...`);
        await originalGeneration.save();
        console.log(`[BG Remove - Function] Save successful for Generation ${originalGeneration._id}.`);
        console.log(`[BG Remove - Function] Generation object imageUrl after save: ${originalGeneration.imageUrl}`);

        // Return the updated generation object (or just the URL)
        return {
            _id: originalGeneration._id.toString(),
            imageUrl: originalGeneration.imageUrl,
            prompt: originalGeneration.prompt,
            status: originalGeneration.status,
            createdAt: originalGeneration.createdAt,
            updatedAt: originalGeneration.updatedAt,
            isUpscaled: originalGeneration.isUpscaled || false
        };

    } catch (dbError) {
        console.error(`[BG Remove - Function] Database update error for original Generation ${originalGeneration._id}:`, dbError.stack || dbError);
        // Throw the error to be handled by the caller
        throw new Error(`Background removed, but failed to update the original generation record: ${dbError.message}`);
    }
    // --- End DB Update ---
}
// --- End Reusable Function ---


// Remove background from an image AND UPDATE DB (Route Handler)
router.post('/bgremove', auth, async (req, res) => {
    console.log('[BG Remove - Router] Request received.');
    try {
        const { imageUrl, generationId } = req.body;
        console.log('[BG Remove - Router] Calling performBackgroundRemoval function...');

        // Call the reusable function
        const updatedGeneration = await performBackgroundRemoval(imageUrl, generationId, req.userId);

        console.log('[BG Remove - Router] performBackgroundRemoval successful. Responding to client.');
        // Respond with the data of the UPDATED generation
        res.status(200).json(updatedGeneration);

    } catch (error) {
        console.error('[BG Remove - Router] Error caught in route handler:', error.stack || error);
        // Determine appropriate status code based on error type if needed
        if (error.message.includes('not found or access denied')) {
            res.status(404).json({ error: error.message });
        } else if (error.message.includes('required')) {
            res.status(400).json({ error: error.message });
        } else {
            res.status(500).json({ error: error.message || 'Failed to remove background' });
        }
    }
});


// Delete an image
router.delete('/:fileName', auth, async (req, res) => {
    try {
        const success = await storage.deleteImage(req.params.fileName);
        if (success) {
            res.json({ success: true });
        } else {
            res.status(404).json({ error: 'Image not found' });
        }
    } catch (error) {
        console.error('Error deleting image:', error);
        res.status(500).json({ error: 'Failed to delete image' });
    }
});

// Get image URL
router.get('/:fileName/url', auth, async (req, res) => {
    try {
        const publicUrl = await storage.getImagePublicUrl(req.params.fileName);
        res.json({
            success: true,
            url: publicUrl
        });
    } catch (error) {
        console.error('Error getting image URL:', error);
        res.status(500).json({ error: 'Failed to get image URL' });
    }
});

export default router;
